defmodule Extractor.Repo.Migrations.CreateTemplateFields do
  use Ecto.Migration

  def change do
    create table(:template_fields) do
      add :field_name, :string
      add :x1, :float
      add :y1, :float
      add :x2, :float
      add :y2, :float
      add :template_id, references(:templates, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:template_fields, [:template_id])
  end
end
