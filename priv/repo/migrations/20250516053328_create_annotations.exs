defmodule Extractor.Repo.Migrations.CreateAnnotations do
  use Ecto.Migration

  def change do
    create table(:annotations) do
      add :field_name, :string
      add :field_value, :string
      add :x1, :float
      add :y1, :float
      add :x2, :float
      add :y2, :float
      add :page_id, references(:pages, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:annotations, [:page_id])
  end
end
