defmodule Extractor.Repo.Migrations.CreatePages do
  use Ecto.Migration

  def change do
    create table(:pages) do
      add :page_number, :integer
      add :text_content, :text
      add :ocr_processed, :boolean, default: false, null: false
      add :document_id, references(:documents, on_delete: :nothing)

      timestamps(type: :utc_datetime)
    end

    create index(:pages, [:document_id])
  end
end
