[project]
name = "python-scripts"
version = "0.1.0"
description = "a script for AI with python"
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "argparse (>=1.4.0,<2.0.0)",
    "pdftotext (>=3.0.0,<4.0.0)",
    "sentencepiece (>=0.2.0,<0.3.0)",
    "transformers (>=4.52.3,<5.0.0)",
    "torch (>=2.7.0,<3.0.0)",
    "pillow (>=11.2.1,<12.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
