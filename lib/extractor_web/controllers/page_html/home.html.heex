<.flash_group flash={@flash} />
<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">Document Extractor</h1>
  </div>

  <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
    <h2 class="text-2xl font-semibold mb-4">Welcome to Document Extractor</h2>
    <p class="text-lg mb-6">
      Upload, annotate, and extract data from your documents with ease. This application allows you to:
    </p>

    <ul class="list-disc pl-6 mb-8 space-y-2">
      <li>Upload PDF and image files</li>
      <li>View documents in the browser</li>
      <li>Define fields by drawing zones or highlighting text</li>
      <li>Map selections to JSON keys</li>
      <li>Build a reusable annotation dataset</li>
      <li>Create templates for standard document layouts</li>
    </ul>

    <div class="flex space-x-4">
      <a
        href="/documents"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
      >
        Manage Documents
      </a>
      <a
        href="/templates"
        class="px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition"
      >
        Manage Templates
      </a>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-xl font-semibold mb-3">Document Management</h3>
      <p class="mb-4">
        Upload and process your documents. The system automatically extracts text from PDFs and performs OCR on images.
      </p>
      <a href="/documents" class="text-blue-600 hover:text-blue-800 font-medium">
        Go to Documents →
      </a>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-xl font-semibold mb-3">Template Management</h3>
      <p class="mb-4">
        Create templates for standard document layouts to automate extraction of data from recurring document types.
      </p>
      <a href="/templates" class="text-blue-600 hover:text-blue-800 font-medium">
        Go to Templates →
      </a>
    </div>
  </div>
</div>
