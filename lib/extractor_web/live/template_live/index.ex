defmodule ExtractorWeb.TemplateLive.Index do
  use ExtractorWeb, :live_view

  alias Extractor.Documents
  alias Extractor.Documents.Template
  alias Extractor.Documents.TemplateField

  @impl true
  def mount(_params, _session, socket) do
    templates = Documents.list_templates()

    {:ok,
     socket
     |> assign(:templates, templates)
     |> assign(:selected_template, nil)
     |> assign(:template_fields, [])
     |> assign(:page, "templates")
     |> assign(:show_form, false)
     |> assign(:form_mode, :new)
     |> assign(:form_data, %{})}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Templates")
  end

  defp apply_action(socket, :view, %{"id" => id}) do
    template = Documents.get_template!(id)
    template_fields = Documents.list_template_fields(template.id)

    socket
    |> assign(:page_title, "View Template")
    |> assign(:selected_template, template)
    |> assign(:template_fields, template_fields)
  end

  @impl true
  def handle_event("view-template", %{"id" => id}, socket) do
    {:noreply, push_patch(socket, to: ~p"/templates/#{id}")}
  end

  @impl true
  def handle_event("delete-template", %{"id" => id}, socket) do
    template = Documents.get_template!(id)
    {:ok, _} = Documents.delete_template(template)

    templates = Documents.list_templates()

    {:noreply, assign(socket, :templates, templates)}
  end

  @impl true
  def handle_event("new-template", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_form, true)
     |> assign(:form_mode, :new)
     |> assign(:form_data, %{})}
  end

  @impl true
  def handle_event("edit-template", %{"id" => id}, socket) do
    template = Documents.get_template!(id)

    {:noreply,
     socket
     |> assign(:show_form, true)
     |> assign(:form_mode, :edit)
     |> assign(:form_data, %{
       id: template.id,
       name: template.name,
       description: template.description
     })}
  end

  @impl true
  def handle_event("cancel-form", _params, socket) do
    {:noreply, assign(socket, :show_form, false)}
  end

  @impl true
  def handle_event("save-template", %{"template" => template_params}, socket) do
    case socket.assigns.form_mode do
      :new ->
        case Documents.create_template(template_params) do
          {:ok, template} ->
            templates = Documents.list_templates()

            {:noreply,
             socket
             |> assign(:templates, templates)
             |> assign(:show_form, false)}

          {:error, _changeset} ->
            {:noreply, socket}
        end

      :edit ->
        template = Documents.get_template!(socket.assigns.form_data.id)

        case Documents.update_template(template, template_params) do
          {:ok, template} ->
            templates = Documents.list_templates()

            {:noreply,
             socket
             |> assign(:templates, templates)
             |> assign(:show_form, false)}

          {:error, _changeset} ->
            {:noreply, socket}
        end
    end
  end

  @impl true
  def handle_event("new-field", _params, socket) do
    template = socket.assigns.selected_template

    if template do
      field_params = %{
        template_id: template.id,
        field_name: "New Field",
        x1: 0.0,
        y1: 0.0,
        x2: 100.0,
        y2: 100.0
      }

      case Documents.create_template_field(field_params) do
        {:ok, _field} ->
          template_fields = Documents.list_template_fields(template.id)
          {:noreply, assign(socket, :template_fields, template_fields)}

        {:error, _changeset} ->
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("delete-field", %{"id" => id}, socket) do
    field = Documents.get_template_field!(id)
    {:ok, _} = Documents.delete_template_field(field)

    template = socket.assigns.selected_template
    template_fields = Documents.list_template_fields(template.id)

    {:noreply, assign(socket, :template_fields, template_fields)}
  end

  @impl true
  def handle_event("update-field", params, socket) do
    field = Documents.get_template_field!(params["id"])

    field_params = %{
      field_name: params["field_name"],
      x1: String.to_float(params["x1"]),
      y1: String.to_float(params["y1"]),
      x2: String.to_float(params["x2"]),
      y2: String.to_float(params["y2"])
    }

    case Documents.update_template_field(field, field_params) do
      {:ok, _field} ->
        template = socket.assigns.selected_template
        template_fields = Documents.list_template_fields(template.id)
        {:noreply, assign(socket, :template_fields, template_fields)}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Template Management</h1>
        <div>
          <.link patch={~p"/documents"} class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 mr-2">
            Documents
          </.link>
          <.link patch={~p"/templates"} class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
            Templates
          </.link>
        </div>
      </div>

      <%= if @selected_template do %>
        <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
          <div class="p-4 border-b flex justify-between items-center">
            <div>
              <h2 class="text-xl font-semibold">{@selected_template.name}</h2>
              <p class="text-gray-500">{@selected_template.description}</p>
            </div>
            <div>
              <button
                phx-click="edit-template"
                phx-value-id={@selected_template.id}
                class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 mr-2"
              >
                Edit
              </button>
              <.link patch={~p"/templates"} class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300">
                Back
              </.link>
            </div>
          </div>

          <div class="p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium">Template Fields</h3>
              <button
                phx-click="new-field"
                class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Add Field
              </button>
            </div>

            <%= if Enum.empty?(@template_fields) do %>
              <div class="text-center py-8">
                <p class="text-gray-500">No fields defined for this template yet.</p>
              </div>
            <% else %>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Field Name
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Coordinates
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for field <- @template_fields do %>
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900">{field.field_name}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-500">
                            ({field.x1}, {field.y1}) to ({field.x2}, {field.y2})
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            phx-click="delete-field"
                            phx-value-id={field.id}
                            data-confirm="Are you sure you want to delete this field?"
                            class="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="bg-white rounded-lg shadow overflow-hidden">
          <div class="p-4 border-b flex justify-between items-center">
            <h2 class="text-xl font-semibold">Templates</h2>
            <button
              phx-click="new-template"
              class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              New Template
            </button>
          </div>

          <div class="p-4">
            <%= if Enum.empty?(@templates) do %>
              <div class="text-center py-8">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-12 w-12 mx-auto text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <p class="mt-2 text-gray-500">
                  No templates yet. Create your first template to get started.
                </p>
              </div>
            <% else %>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Name
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Description
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Fields
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for template <- @templates do %>
                      <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900">{template.name}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-500">{template.description}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-500">
                            {length(Documents.list_template_fields(template.id))} fields
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            phx-click="view-template"
                            phx-value-id={template.id}
                            class="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            View
                          </button>
                          <button
                            phx-click="edit-template"
                            phx-value-id={template.id}
                            class="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            phx-click="delete-template"
                            phx-value-id={template.id}
                            data-confirm="Are you sure you want to delete this template?"
                            class="text-red-600 hover:text-red-900"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% end %>
          </div>
        </div>

        <%= if @show_form do %>
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium">
                  {if @form_mode == :new, do: "New Template", else: "Edit Template"}
                </h3>
                <button phx-click="cancel-form" class="text-gray-500 hover:text-gray-700">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <form phx-submit="save-template">
                <div class="mb-4">
                  <label for="template_name" class="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    id="template_name"
                    name="template[name]"
                    value={@form_data[:name]}
                    class="w-full px-3 py-2 border rounded"
                    required
                  />
                </div>

                <div class="mb-4">
                  <label
                    for="template_description"
                    class="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Description
                  </label>
                  <textarea
                    id="template_description"
                    name="template[description]"
                    rows="3"
                    class="w-full px-3 py-2 border rounded"
                  ><%= @form_data[:description] %></textarea>
                </div>

                <div class="flex justify-end">
                  <button
                    type="button"
                    phx-click="cancel-form"
                    class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 mr-2"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Save
                  </button>
                </div>
              </form>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end
end
