defmodule ExtractorWeb.DocumentLive.Index do
  use ExtractorWeb, :live_view

  alias Extractor.Documents
  alias Extractor.Documents.Document

  @impl true
  def mount(_params, _session, socket) do
    documents = Documents.list_documents()

    {:ok,
     socket
     |> assign(:documents, documents)
     |> assign(:selected_document, nil)
     |> assign(:page, "documents")}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Documents")
  end

  defp apply_action(socket, :view, %{"id" => id}) do
    document = Documents.get_document!(id)

    socket
    |> assign(:page_title, "View Document")
    |> assign(:selected_document, document)
  end

  @impl true
  def handle_event("view-document", %{"id" => id}, socket) do
    {:noreply, push_patch(socket, to: ~p"/documents/#{id}")}
  end

  @impl true
  def handle_event("delete-document", %{"id" => id}, socket) do
    document = Documents.get_document!(id)
    {:ok, _} = Documents.delete_document(document)

    documents = Documents.list_documents()

    {:noreply, assign(socket, :documents, documents)}
  end

  @impl true
  def handle_event(
        "selection_made",
        %{"text" => text, "x1" => x1, "x2" => x2, "y1" => y1, "y2" => y2},
        socket
      ) do
    # We're not using this event anymore, as we're using selections_updated instead
    # to handle multiple selections
    {:noreply, socket}
  end

  @impl true
  def handle_info({:document_uploaded, document_id}, socket) do
    documents = Documents.list_documents()

    {:noreply,
     socket
     |> assign(:documents, documents)
     |> push_patch(to: ~p"/documents/#{document_id}")}
  end

  @impl true
  def handle_info({:selection_made, selection}, socket) do
    send_update(ExtractorWeb.DocumentLive.ViewerComponent,
      id: "document-viewer",
      selection: selection
    )

    {:noreply, socket}
  end

  @impl true
  def handle_event("selections_updated", %{"selections" => selections}, socket) do
    # This event is triggered when the selections are updated in the JavaScript hook
    # Forward the selections to the viewer component
    send_update(ExtractorWeb.DocumentLive.ViewerComponent,
      id: "document-viewer",
      all_selections: selections
    )

    {:noreply, socket}
  end

  @impl true
  def handle_event("selections_cleared", _params, socket) do
    # This event is triggered when the selections are cleared in the JavaScript hook
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Document Extractor</h1>
        <div>
          <.link patch={~p"/documents"} class="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
            Documents
          </.link>
        </div>
      </div>

      <%= if @selected_document do %>
        <div class="h-[calc(100vh-8rem)]">
          <.live_component
            module={ExtractorWeb.DocumentLive.ViewerComponent}
            id="document-viewer"
            document={@selected_document}
          />
        </div>
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="md:col-span-2">
            <div class="bg-white rounded-lg shadow overflow-hidden">
              <div class="p-4 border-b">
                <h2 class="text-xl font-semibold">Documents</h2>
              </div>

              <div class="p-4">
                <%= if Enum.empty?(@documents) do %>
                  <div class="text-center py-8">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-12 w-12 mx-auto text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p class="mt-2 text-gray-500">
                      No documents yet. Upload your first document to get started.
                    </p>
                  </div>
                <% else %>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Filename
                          </th>
                          <th
                            scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Type
                          </th>
                          <th
                            scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Status
                          </th>
                          <th
                            scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Uploaded
                          </th>
                          <th
                            scope="col"
                            class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <%= for document <- @documents do %>
                          <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm font-medium text-gray-900">{document.filename}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="text-sm text-gray-500">{document.content_type}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <%= if document.processed do %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                  Processed
                                </span>
                              <% else %>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                  Processing
                                </span>
                              <% end %>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {Calendar.strftime(document.inserted_at, "%Y-%m-%d %H:%M")}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                phx-click="view-document"
                                phx-value-id={document.id}
                                class="text-blue-600 hover:text-blue-900 mr-3"
                              >
                                View
                              </button>
                              <button
                                phx-click="delete-document"
                                phx-value-id={document.id}
                                data-confirm="Are you sure you want to delete this document?"
                                class="text-red-600 hover:text-red-900"
                              >
                                Delete
                              </button>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <div>
            <.live_component
              module={ExtractorWeb.DocumentLive.UploadComponent}
              id="document-uploader"
            />
          </div>
        </div>
      <% end %>
    </div>
    """
  end
end
