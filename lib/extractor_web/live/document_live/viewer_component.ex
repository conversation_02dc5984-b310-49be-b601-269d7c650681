defmodule ExtractorWeb.DocumentLive.ViewerComponent do
  use ExtractorWeb, :live_component

  alias Extractor.Documents
  alias Extractor.Documents.Document
  alias Extractor.Documents.Page
  alias Extractor.Documents.Annotation
  alias Extractor.OCR

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex flex-col h-full">
      <div class="flex-1 overflow-hidden">
        <div class="flex flex-col h-full">
          <div class="mb-2 flex justify-end">
            <button
              phx-click="clear-selections"
              phx-target={@myself}
              class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 shadow"
            >
              Clear Selections
            </button>
          </div>

          <div class="relative flex-1 overflow-auto" style="height: 80vh;">
            <%= if @document.content_type == "application/pdf" do %>
              <div
                id="pdf-viewer"
                class="w-full h-full"
                phx-hook="PdfViewer"
                data-document-id={@document.id}
                data-file-path={@document.file_path}
              >
                <div id="pdf-container" class="w-full h-full relative">
                  <canvas id="pdf-canvas" class="w-full h-full"></canvas>
                  <div
                    id="annotation-layer"
                    class="absolute top-0 left-0 w-full h-full pointer-events-none"
                  >
                  </div>
                  <div
                    id="selection-layer"
                    class="absolute top-0 left-0 w-full h-full"
                    phx-hook="SelectionHandler"
                    style="pointer-events: auto;"
                  >
                  </div>
                </div>
              </div>
            <% else %>
              <div
                id="image-viewer"
                class="w-full h-full overflow-auto"
                phx-hook="ImageViewer"
                data-document-id={@document.id}
                data-file-path={@document.file_path}
              >
                <div class="relative">
                  <img src={"/#{@document.file_path}"} class="block" style="max-width: 100%;" />
                  <div
                    id="annotation-layer"
                    class="absolute top-0 left-0 w-full h-full pointer-events-none"
                  >
                  </div>
                  <div
                    id="selection-layer"
                    class="absolute top-0 left-0 w-full h-full"
                    phx-hook="SelectionHandler"
                    style="pointer-events: auto;"
                  >
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <div class="bg-white border-t p-4">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-lg font-semibold">{@document.filename}</h3>
            <p class="text-sm text-gray-500">
              <%= if @current_page do %>
                Page {@current_page.page_number} of {@total_pages}
              <% else %>
                No pages available
              <% end %>
            </p>
          </div>

          <div class="flex space-x-2">
            <button
              phx-click="prev-page"
              phx-target={@myself}
              class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
              disabled={@current_page_num <= 1}
            >
              Previous
            </button>
            <button
              phx-click="next-page"
              phx-target={@myself}
              class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
              disabled={@current_page_num >= @total_pages}
            >
              Next
            </button>
          </div>
        </div>

        <div class="flex space-x-4">
          <div class="w-1/2">
            <h4 class="font-medium mb-2">Extracted Text</h4>
            <div class="border rounded p-3 h-40 overflow-y-auto bg-gray-50">
              <%= if @current_page do %>
                <p class="whitespace-pre-line text-sm">{@current_page.text_content}</p>
              <% else %>
                <p class="text-gray-500 italic">No text available</p>
              <% end %>
            </div>
          </div>

          <div class="w-1/2">
            <h4 class="font-medium mb-2">Annotations</h4>
            <div class="border rounded p-3 h-40 overflow-y-auto bg-gray-50">
              <%= if Enum.empty?(@annotations) do %>
                <p class="text-gray-500 italic">
                  No annotations yet. Select text or draw a zone to create one.
                </p>
              <% else %>
                <ul class="space-y-2">
                  <%= for annotation <- @annotations do %>
                    <li class="text-sm border-b pb-2">
                      <div class="flex justify-between">
                        <span class="font-medium">{annotation.field_name}</span>
                        <button
                          phx-click="delete-annotation"
                          phx-target={@myself}
                          phx-value-id={annotation.id}
                          class="text-red-500 hover:text-red-700"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                      <p class="text-gray-700">{annotation.field_value || "No value"}</p>
                      <p class="text-xs text-gray-500">
                        Coordinates: ({annotation.x1}, {annotation.y1}) to ({annotation.x2}, {annotation.y2})
                      </p>
                    </li>
                  <% end %>
                </ul>
              <% end %>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <h4 class="font-medium mb-2">Create Annotations</h4>
          <form phx-submit="create-annotations" phx-target={@myself} class="flex items-end space-x-2">
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Save
            </button>
          </form>
          <form
            phx-submit="ai-create-annotations"
            phx-target={@myself}
            class="flex items-end space-x-2"
          >
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              AI Magic Save
            </button>
          </form>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def update(%{document: document} = assigns, socket) do
    pages = Documents.list_pages(document.id)
    current_page = List.first(pages)

    annotations = if current_page, do: Documents.list_annotations(current_page.id), else: []

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:document, document)
     |> assign(:pages, pages)
     |> assign(:total_pages, length(pages))
     |> assign(:current_page, current_page)
     |> assign(:current_page_num, if(current_page, do: current_page.page_number, else: 0))
     |> assign(:annotations, annotations)
     # |> assign(:selection, %{x1: 0, y1: 0, x2: 0, y2: 0, text: ""})
     |> assign(:selection_active, false)
     |> assign(:all_selections, [])}
  end

  ## deprecated
  @impl true
  def update(%{selection: selection}, socket) do
    selection_active = selection.x1 != selection.x2 && selection.y1 != selection.y2

    {:ok,
     socket
     |> assign(:selection, selection)
     |> assign(:selection_active, selection_active)}
  end

  @impl true
  def update(%{all_selections: selections}, socket) do
    IO.puts("Received #{length(selections)} selections in viewer component")
    # Store all selections in the socket
    {:ok, assign(socket, :all_selections, selections)}
  end

  @impl true
  def handle_event("prev-page", _params, socket) do
    current_page_num = socket.assigns.current_page_num

    if current_page_num > 1 do
      new_page_num = current_page_num - 1
      new_page = Enum.find(socket.assigns.pages, &(&1.page_number == new_page_num))
      annotations = Documents.list_annotations(new_page.id)

      {:noreply,
       socket
       |> assign(:current_page, new_page)
       |> assign(:current_page_num, new_page_num)
       |> assign(:annotations, annotations)
       |> assign(:selection, %{x1: 0, y1: 0, x2: 0, y2: 0, text: ""})
       |> assign(:selection_active, false)
       |> push_event("change-page", %{page: new_page_num})}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("next-page", _params, socket) do
    current_page_num = socket.assigns.current_page_num
    total_pages = socket.assigns.total_pages

    if current_page_num < total_pages do
      new_page_num = current_page_num + 1
      new_page = Enum.find(socket.assigns.pages, &(&1.page_number == new_page_num))
      annotations = Documents.list_annotations(new_page.id)

      {:noreply,
       socket
       |> assign(:current_page, new_page)
       |> assign(:current_page_num, new_page_num)
       |> assign(:annotations, annotations)
       |> assign(:selection, %{x1: 0, y1: 0, x2: 0, y2: 0, text: ""})
       |> assign(:selection_active, false)
       |> push_event("change-page", %{page: new_page_num})}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event(
        "create-annotations",
        _params,
        %{assigns: %{all_selections: all_selections, document: document}} = socket
      ) do
    current_page = socket.assigns.current_page || %{}

    #    if current_page do
    created_annotations =
      all_selections
      |> Enum.map(fn selection ->
        # Extract coordinates from the selection - use the actual image coordinates
        x1 = selection["x1"]
        y1 = selection["y1"]
        x2 = selection["x2"]
        y2 = selection["y2"]

        # Log the coordinates for debugging
        IO.puts(
          "Creating annotation with actual image coordinates: x1=#{x1}, y1=#{y1}, x2=#{x2}, y2=#{y2}"
        )

        annotation_params = %{
          #          page_id: current_page.id,
          # Default field name
          field_name: "Annotation #{selection["id"] + 1}",
          # Default empty value
          field_value: selection["text"] || "",
          x1: x1,
          y1: y1,
          x2: x2,
          y2: y2
        }

        Documents.create_annotation(annotation_params)
        |> case do
          {:ok, annotation} ->
            annotation

          {:error, changeset} ->
            IO.inspect(changeset, label: [IO.ANSI.magenta(), "chg"])
            nil
        end
      end)
      |> Enum.reject(&is_nil/1)

    created_annotations
    |> Enum.each(fn %Annotation{x1: x1, y1: y1, x2: x2, y2: y2} = _annotation ->
      Task.Supervisor.start_child(Extractor.TaskSupervisor, fn ->
        OCR.perform_zonal_ocr(document.file_path, x1, y1, x2, y2)
        |> IO.inspect(label: [IO.ANSI.magenta(), "zonalocr"])
      end)
    end)

    IO.inspect(created_annotations, label: [IO.ANSI.yellow(), "Created annotations"])

    {:noreply,
     socket
     |> assign(:annotations, created_annotations)
     |> assign(:all_selections, [])
     |> push_event("clear-selections", %{})}

    # else
    #  IO.inspect("No current page #{inspect(current_page)}", label: [IO.ANSI.yellow()])
    #  {:noreply, socket}
    # end
  end

  def handle_event(
        "ai-create-annotations",
        _params,
        %{assigns: %{all_selections: [], document: document}} = socket
      ) do
    current_page = socket.assigns.current_page || %{}

    Task.Supervisor.start_child(Extractor.TaskSupervisor, fn ->
      OCR.perform_ai_ocr(document)
      |> IO.inspect(label: [IO.ANSI.magenta(), "handle-ai-ocr"])
    end)

    {:noreply,
     socket
     |> assign(:all_selections, [])
     |> push_event("clear-selections", %{})}
  end

  ## deprecate
  @impl true
  def handle_event("create-annotation", params, socket) do
    current_page = socket.assigns.current_page

    if current_page do
      annotation_params = %{
        page_id: current_page.id,
        field_name: params["field_name"],
        field_value: params["selected_text"],
        x1: String.to_float(params["x1"]),
        y1: String.to_float(params["y1"]),
        x2: String.to_float(params["x2"]),
        y2: String.to_float(params["y2"])
      }

      case Documents.create_annotation(annotation_params) do
        {:ok, _annotation} ->
          annotations = Documents.list_annotations(current_page.id)

          {:noreply,
           socket
           |> assign(:annotations, annotations)
           |> assign(:selection, %{x1: 0, y1: 0, x2: 0, y2: 0, text: ""})
           |> assign(:selection_active, false)
           |> push_event("clear-selection", %{})}

        {:error, _changeset} ->
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("delete-annotation", %{"id" => id}, socket) do
    annotation = Documents.get_annotation!(id)
    {:ok, _} = Documents.delete_annotation(annotation)

    current_page = socket.assigns.current_page
    annotations = Documents.list_annotations(current_page.id)

    {:noreply, assign(socket, :annotations, annotations)}
  end

  @impl true
  def handle_event("clear-selections", _params, socket) do
    IO.puts("Clear selections event received")
    # Also clear the selections in the socket state
    {:noreply,
     socket
     |> assign(:all_selections, [])
     |> push_event("clear-selections", %{})}
  end

  @impl true
  def handle_event("selections_updated", %{"selections" => selections}, socket) do
    # Store the selections in the socket for future use
    # This could be used to create multiple annotations at once
    {:noreply, assign(socket, :all_selections, selections)}
  end

  @impl true
  def handle_event("selections_cleared", _params, socket) do
    {:noreply, assign(socket, :all_selections, [])}
  end
end
