defmodule ExtractorWeb.DocumentLive.UploadComponent do
  use ExtractorWeb, :live_component

  alias Extractor.Documents
  alias Extractor.Documents.Document
  alias Extractor.OCR

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <div class="p-4 bg-white rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-4">Upload Document</h2>

        <form
          id="upload-form"
          action="#"
          phx-submit="save"
          phx-change="validate"
          phx-drop-target={@uploads.document.ref}
          phx-target={@myself}
        >
          <div class="mb-4">
            <div class="flex items-center justify-center w-full">
              <.live_file_input upload={@uploads.document} class="hidden" />
              <label
                for={@uploads.document.ref}
                class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
              >
                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                  <svg
                    class="w-10 h-10 mb-3 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    >
                    </path>
                  </svg>
                  <p class="mb-2 text-sm text-gray-500">
                    <span class="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p class="text-xs text-gray-500">PDF, PNG, JPG or JPEG (MAX. 10MB)</p>
                </div>
              </label>
            </div>
          </div>

          <%= for entry <- @uploads.document.entries do %>
            <div class="mb-4">
              <div class="flex items-center space-x-2">
                <div class="flex-1">
                  <div class="text-sm font-medium">{entry.client_name}</div>
                  <div class="text-xs text-gray-500">{entry.client_type}</div>
                  <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="bg-blue-600 h-2.5 rounded-full" style={"width: #{entry.progress}%"}>
                    </div>
                  </div>
                </div>
                <button
                  type="button"
                  phx-click="cancel-upload"
                  phx-target={@myself}
                  phx-value-ref={entry.ref}
                  class="text-red-500 hover:text-red-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </button>
              </div>

              <%= for err <- upload_errors(@uploads.document, entry) do %>
                <div class="text-red-500 text-xs mt-1">{error_to_string(err)}</div>
              <% end %>
            </div>
            <div class="mb-4">
              <h2>Preview</h2>
              <div class="flex items-center space-x-2">
                <div class="flex-1">
                  <.live_img_preview entry={entry} class="preview" />
                </div>
              </div>
            </div>
          <% end %>

          <div class="flex justify-end">
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              disabled={Enum.empty?(@uploads.document.entries)}
            >
              Upload
            </button>
          </div>
        </form>
      </div>
    </div>
    """
  end

  @impl true
  def mount(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> allow_upload(:document,
       accept: ~w(.pdf .jpg .jpeg .png),
       max_entries: 1,
       max_file_size: 10_000_000
     )}
  end

  @impl true
  def update(assigns, socket) do
    {:ok,
     socket
     |> assign(assigns)
     |> allow_upload(:document,
       accept: ~w(.pdf .jpg .jpeg .png),
       max_entries: 1,
       max_file_size: 10_000_000
     )}
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel-upload", %{"ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, :document, ref)}
  end

  @impl true
  def handle_event("save", _params, socket) do
    IO.puts("Save event triggered")

    uploaded_files =
      consume_uploaded_entries(socket, :document, fn %{path: path}, entry ->
        IO.puts("Processing uploaded file: #{path}")

        # Copy the file to a permanent location
        file_type = Path.extname(entry.client_name)
        filename = "#{:rand.uniform(1_000_000)}-#{Path.basename(path)}#{file_type}"
        dest = Path.join(["uploads", "documents", filename])
        dest_full_path = Path.join(["priv", "static", dest])

        # Ensure directory exists
        File.mkdir_p!(Path.dirname(dest_full_path))

        # Copy file
        IO.puts("Copying file from #{path} to #{dest_full_path}")
        File.cp!(path, dest_full_path)

        # Create a document record
        IO.puts("Creating document record")

        {:ok, document} =
          Documents.create_document(%{
            filename: entry.client_name,
            content_type: entry.client_type,
            file_path: dest,
            processed: false
          })

        # Process the document with OCR
        IO.puts("Starting OCR processing")
        Task.start(fn -> OCR.process_document(document) end)

        {:ok, document.id}
      end)

    IO.puts("Uploaded files: #{inspect(uploaded_files)}")

    # Send the uploaded document ID to the parent LiveView
    if uploaded_files != [] do
      send(self(), {:document_uploaded, List.first(uploaded_files)})
    end

    {:noreply, socket}
  end

  defp error_to_string(:too_large), do: "File is too large (max 10MB)"
  defp error_to_string(:too_many_files), do: "You can only upload 1 file at a time"
  defp error_to_string(:not_accepted), do: "You can only upload PDF, JPG, JPEG or PNG files"
end
