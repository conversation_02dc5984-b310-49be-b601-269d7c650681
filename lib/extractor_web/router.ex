defmodule ExtractorWeb.Router do
  use ExtractorWeb, :router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {ExtractorWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
  end

  pipeline :api do
    plug :accepts, ["json"]
  end

  scope "/", ExtractorWeb do
    pipe_through :browser

    get "/", PageController, :home

    live "/documents", DocumentLive.Index, :index
    live "/documents/:id", DocumentLive.Index, :view

    live "/templates", TemplateLive.Index, :index
    live "/templates/:id", TemplateLive.Index, :view
  end

  # Other scopes may use custom stacks.
  # scope "/api", ExtractorWeb do
  #   pipe_through :api
  # end
end
