defmodule Extractor.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      ExtractorWeb.Telemetry,
      Extractor.Repo,
      {DNSCluster, query: Application.get_env(:extractor, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Extractor.PubSub},
      # Start a worker by calling: Extractor.Worker.start_link(arg)
      # {Extractor.Worker, arg},
      # Start to serve requests, typically the last entry
      {Task.Supervisor, name: Extractor.TaskSupervisor},
      ExtractorWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Extractor.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    ExtractorWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
