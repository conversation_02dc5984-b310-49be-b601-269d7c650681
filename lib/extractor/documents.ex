defmodule Extractor.Documents do
  @moduledoc """
  The Documents context.
  """

  import Ecto.Query, warn: false
  alias Extractor.Repo

  alias Extractor.Documents.Document
  alias Extractor.Documents.Page
  alias Extractor.Documents.Annotation
  alias Extractor.Documents.Template
  alias Extractor.Documents.TemplateField

  # Document functions

  @doc """
  Returns the list of documents.
  """
  def list_documents do
    Repo.all(Document)
  end

  @doc """
  Gets a single document.
  """
  def get_document!(id), do: Repo.get!(Document, id)

  @doc """
  Creates a document.
  """
  def create_document(attrs \\ %{}) do
    %Document{}
    |> Document.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a document.
  """
  def update_document(%Document{} = document, attrs) do
    document
    |> Document.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a document.
  """
  def delete_document(%Document{} = document) do
    Repo.delete(document)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking document changes.
  """
  def change_document(%Document{} = document, attrs \\ %{}) do
    Document.changeset(document, attrs)
  end

  # Page functions

  @doc """
  Returns the list of pages for a document.
  """
  def list_pages(document_id) do
    Page
    |> where([p], p.document_id == ^document_id)
    |> order_by([p], p.page_number)
    |> Repo.all()
  end

  @doc """
  Gets a single page.
  """
  def get_page!(id), do: Repo.get!(Page, id)

  @doc """
  Creates a page.
  """
  def create_page(attrs \\ %{}) do
    %Page{}
    |> Page.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a page.
  """
  def update_page(%Page{} = page, attrs) do
    page
    |> Page.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a page.
  """
  def delete_page(%Page{} = page) do
    Repo.delete(page)
  end

  # Annotation functions

  @doc """
  Returns the list of annotations for a page.
  """
  def list_annotations(page_id) do
    Annotation
    |> where([a], a.page_id == ^page_id)
    |> Repo.all()
  end

  @doc """
  Gets a single annotation.
  """
  def get_annotation!(id), do: Repo.get!(Annotation, id)

  @doc """
  Creates an annotation.
  """
  def create_annotation(attrs \\ %{}) do
    %Annotation{}
    |> Annotation.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an annotation.
  """
  def update_annotation(%Annotation{} = annotation, attrs) do
    annotation
    |> Annotation.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes an annotation.
  """
  def delete_annotation(%Annotation{} = annotation) do
    Repo.delete(annotation)
  end

  # Template functions

  @doc """
  Returns the list of templates.
  """
  def list_templates do
    Repo.all(Template)
  end

  @doc """
  Gets a single template.
  """
  def get_template!(id), do: Repo.get!(Template, id)

  @doc """
  Creates a template.
  """
  def create_template(attrs \\ %{}) do
    %Template{}
    |> Template.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a template.
  """
  def update_template(%Template{} = template, attrs) do
    template
    |> Template.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a template.
  """
  def delete_template(%Template{} = template) do
    Repo.delete(template)
  end

  # Template Field functions

  @doc """
  Returns the list of template fields for a template.
  """
  def list_template_fields(template_id) do
    TemplateField
    |> where([tf], tf.template_id == ^template_id)
    |> Repo.all()
  end

  @doc """
  Gets a single template field.
  """
  def get_template_field!(id), do: Repo.get!(TemplateField, id)

  @doc """
  Creates a template field.
  """
  def create_template_field(attrs \\ %{}) do
    %TemplateField{}
    |> TemplateField.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a template field.
  """
  def update_template_field(%TemplateField{} = template_field, attrs) do
    template_field
    |> TemplateField.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a template field.
  """
  def delete_template_field(%TemplateField{} = template_field) do
    Repo.delete(template_field)
  end
end
