defmodule Extractor.Documents.Page do
  use Ecto.Schema
  import Ecto.Changeset

  schema "pages" do
    field :page_number, :integer
    field :text_content, :string
    field :ocr_processed, :boolean, default: false

    belongs_to :document, Extractor.Documents.Document
    has_many :annotations, Extractor.Documents.Annotation

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(page, attrs) do
    page
    |> cast(attrs, [:page_number, :text_content, :ocr_processed, :document_id])
    |> validate_required([:page_number, :document_id])
    |> foreign_key_constraint(:document_id)
  end
end
