defmodule Extractor.Documents.TemplateField do
  use Ecto.Schema
  import Ecto.Changeset

  schema "template_fields" do
    field :field_name, :string
    field :x1, :float
    field :y1, :float
    field :x2, :float
    field :y2, :float

    belongs_to :template, Extractor.Documents.Template

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(template_field, attrs) do
    template_field
    |> cast(attrs, [:field_name, :x1, :y1, :x2, :y2, :template_id])
    |> validate_required([:field_name, :x1, :y1, :x2, :y2, :template_id])
    |> foreign_key_constraint(:template_id)
  end
end
