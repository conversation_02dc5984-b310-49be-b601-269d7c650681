defmodule Extractor.Documents.Template do
  use Ecto.Schema
  import Ecto.Changeset

  schema "templates" do
    field :name, :string
    field :description, :string

    has_many :template_fields, Extractor.Documents.TemplateField

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(template, attrs) do
    template
    |> cast(attrs, [:name, :description])
    |> validate_required([:name])
  end
end
