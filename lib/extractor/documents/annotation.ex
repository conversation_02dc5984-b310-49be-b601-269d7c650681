defmodule Extractor.Documents.Annotation do
  use Ecto.Schema
  import Ecto.Changeset

  schema "annotations" do
    field :field_value, :string
    field :field_name, :string
    field :x1, :float
    field :y1, :float
    field :x2, :float
    field :y2, :float

    belongs_to :page, Extractor.Documents.Page

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(annotation, attrs) do
    annotation
    |> cast(attrs, [:field_name, :field_value, :x1, :y1, :x2, :y2, :page_id])
    # |> validate_required([:x1, :y1, :x2, :y2, :page_id])
    |> validate_required([:x1, :y1, :x2, :y2])
    |> foreign_key_constraint(:page_id)
  end
end
