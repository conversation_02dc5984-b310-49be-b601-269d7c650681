defmodule Extractor.Documents.Document do
  use Ecto.Schema
  import Ecto.Changeset
  use Waffle.Ecto.Schema

  schema "documents" do
    field :filename, :string
    field :file_path, :string
    field :content_type, :string
    field :processed, :boolean, default: false
    field :file, Extractor.DocumentFile.Type

    has_many :pages, Extractor.Documents.Page

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(document, attrs) do
    document
    |> cast(attrs, [:filename, :content_type, :file_path, :processed])
    |> cast_attachments(attrs, [:file])
    |> validate_required([:filename, :content_type])
  end
end
