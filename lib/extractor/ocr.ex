defmodule Extractor.OCR do
  @moduledoc """
  Module for handling OCR operations.
  """

  alias Hex.Shell.Process
  alias Extractor.Documents
  alias Extractor.Documents.Document
  alias Extractor.Documents.Page

  @doc """
  Process a document with OCR.
  """
  def process_document(%Document{} = document) do
    IO.puts("Processing document: #{inspect(document)}")

    # Get the full path to the file
    full_path = Path.join(["priv", "static", document.file_path])
    IO.puts("Full path: #{full_path}")

    case document.content_type do
      "application/pdf" ->
        process_pdf(%{document | file_path: full_path})

      "image/jpeg" ->
        process_image(%{document | file_path: full_path})

      "image/png" ->
        process_image(%{document | file_path: full_path})

      _ ->
        IO.puts("Unsupported file type: #{document.content_type}")
        {:error, "Unsupported file type"}
    end
  end

  @doc """
  Process a PDF document.
  """
  defp process_pdf(%Document{} = document) do
    # Get the file path
    file_path = document.file_path

    # Extract text from PDF
    case extract_text_from_pdf(file_path) do
      {:ok, pages_text} ->
        # Create pages with extracted text
        create_pages_with_text(document, pages_text)

        # Mark document as processed
        Documents.update_document(document, %{processed: true})

        {:ok, document}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Process an image document.
  """
  defp process_image(%Document{} = document) do
    # Get the file path
    file_path = document.file_path

    # Perform OCR on the image
    case perform_ocr_on_image(file_path) do
      {:ok, text} ->
        # Create a single page with the extracted text
        {:ok, page} =
          Documents.create_page(%{
            document_id: document.id,
            page_number: 1,
            text_content: text,
            ocr_processed: true
          })

        # Mark document as processed
        Documents.update_document(document, %{processed: true})

        {:ok, document}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Extract text from a PDF file.
  """
  defp extract_text_from_pdf(file_path) do
    # For simplicity, we'll assume all PDFs need OCR
    # In a real application, you would use PdfInfo to check if the PDF has text content
    extract_text_from_scanned_pdf(file_path)
  end

  @doc """
  Extract text from a vector PDF (with text content).
  """
  defp extract_text_from_vector_pdf(file_path) do
    # Use external tool to extract text (e.g., pdftotext)
    # This is a simplified example
    case System.cmd("pdftotext", [file_path, "-"]) do
      {output, 0} ->
        # Split the output by page markers (simplified)
        pages =
          String.split(output, "Page ")
          # Drop the first empty part
          |> Enum.drop(1)
          |> Enum.map(fn page_text ->
            # Clean up the text
            String.trim(page_text)
          end)

        {:ok, pages}

      {_, error_code} ->
        {:error, "Failed to extract text from PDF: error code #{error_code}"}
    end
  end

  @doc """
  Extract text from a scanned PDF using OCR.
  """
  defp extract_text_from_scanned_pdf(file_path) do
    # Convert PDF to images and perform OCR on each page
    # This is a simplified example
    case System.cmd("pdfinfo", [file_path]) do
      {output, 0} ->
        # Parse the number of pages
        pages_line = Regex.run(~r/Pages:\s+(\d+)/, output)
        num_pages = if pages_line, do: String.to_integer(Enum.at(pages_line, 1)), else: 0

        # Process each page
        pages_text =
          Enum.map(1..num_pages, fn page_num ->
            # Convert PDF page to image
            temp_image = "/tmp/page_#{page_num}.png"

            System.cmd("pdftoppm", [
              "-png",
              "-f",
              "#{page_num}",
              "-l",
              "#{page_num}",
              file_path,
              "/tmp/page"
            ])

            # Perform OCR on the image
            {:ok, text} = perform_ocr_on_image(temp_image)

            # Clean up
            File.rm(temp_image)

            text
          end)

        {:ok, pages_text}

      {_, error_code} ->
        {:error, "Failed to process scanned PDF: error code #{error_code}"}
    end
  end

  @doc """
  Perform OCR on an image.
  """
  defp perform_ocr_on_image(file_path) do
    case TesseractOcr.read(file_path) do
      {:ok, text} -> {:ok, text}
      {:error, reason} -> {:error, reason}
      e -> {:error, "Failed to perform OCR due to: #{inspect(e)}"}
    end
  end

  @doc """
  Create pages with extracted text.
  """
  defp create_pages_with_text(%Document{} = document, pages_text) do
    Enum.with_index(pages_text, 1)
    |> Enum.each(fn {text, page_num} ->
      Documents.create_page(%{
        document_id: document.id,
        page_number: page_num,
        text_content: text,
        ocr_processed: true
      })
    end)
  end

  @doc """
  Perform zonal OCR on a specific region of an image.
  """
  def perform_zonal_ocr(file_path, x1, y1, x2, y2) do
    # Create a temporary cropped image
    # https://github.com/tesseract-ocr/tesseract/issues/4333
    cwd = File.cwd!()
    base_path = Path.join(cwd, "/priv/static/uploads/cropped")
    File.mkdir_p!(base_path)
    temp_crop = Path.join(base_path, "/crop_#{:rand.uniform(1000)}.png")

    # Crop the image to the specified zone
    IO.inspect(file_path, label: [IO.ANSI.magenta(), "zonal_ocr_file"])
    image = Image.open!(Path.join([cwd, "priv/static", file_path]))
    IO.inspect(image, label: [IO.ANSI.cyan(), "Image.open!"])

    # Log the coordinates for debugging
    IO.puts("OCR cropping with coordinates: x1=#{x1}, y1=#{y1}, x2=#{x2}, y2=#{y2}")

    # Ensure coordinates are within image bounds
    image_width = Image.width(image)
    image_height = Image.height(image)

    # Ensure coordinates are within image bounds
    x1 = max(0, min(x1, image_width))
    y1 = max(0, min(y1, image_height))
    x2 = max(0, min(x2, image_width))
    y2 = max(0, min(y2, image_height))

    # Ensure x1 < x2 and y1 < y2
    {x1, x2} = if x1 > x2, do: {x2, x1}, else: {x1, x2}
    {y1, y2} = if y1 > y2, do: {y2, y1}, else: {y1, y2}

    # Log the adjusted coordinates
    IO.puts("Adjusted coordinates: x1=#{x1}, y1=#{y1}, x2=#{x2}, y2=#{y2}")

    # Crop the image using polygon coordinates
    {:ok, crop_image} =
      Image.crop(image, [
        {round(x1), round(y1)},
        {round(x2), round(y1)},
        {round(x2), round(y2)},
        {round(x1), round(y2)}
      ])

    # Perform OCR on the cropped image
    new_img = Image.write!(crop_image, temp_crop, suffix: ".png")
    result = TesseractOcr.read(temp_crop)
    # Clean up
    File.rm(temp_crop)

    result
  end

  @doc """
  Use AI to perform OCR (on image). No annotations have been pre-selected.
  """
  def perform_ai_ocr(%Document{file_path: file_path}) do
    cwd = File.cwd!()
    working_dir = Path.join([cwd, "python_scripts"])
    new_file_path = Path.join([cwd, "priv/static", file_path])
    file_ext = Path.extname(new_file_path)
    File.cd!(working_dir)
    port = Port.open({:spawn, "poetry run python #{new_file_path}#{file_ext}"}, [:binary])
    Port.monitor(port)
    File.cd!(cwd)
    receive do
      {^port, {:data, text}} ->
        IO.inspect(text, label: [IO.ANSI.yellow, "ai_ocr"])
        text
      {:DOWN, ref, :port, object, reason} -> 
        IO.inspect({ref, object, reason}, label: [IO.ANSI.magenta(), "DOWN "])
        {:error, "Port exited with reason: #{inspect(reason)}"}
      e -> {:error, "Failed to perform AI text extraction due to #{inspect(e)}"}
    end
  end

  defp tesseract_ocr(%Document{file_path: file_path}) do
    # ffprobe -v error -select_streams v:0 -show_entries stream=width,height -of csv=p=0 input_image.png
    cwd = File.cwd!()
    new_file_path = Path.join(["priv/static", file_path])
    IO.inspect(File.exists?(new_file_path), label: [IO.ANSI.magenta(), "file exists path"])
    file_ext = Path.extname(new_file_path)

    case System.cmd("ffprobe", [
           "-v",
           "error",
           "-select_streams",
           "v:0",
           "-show_entries",
           "stream=width,height",
           "-of",
           "csv=p=0",
           new_file_path
         ]) do
      {output, 0} ->
        [width, height] = output |> String.trim() |> String.split(",")

      {error, 1} ->
        {:error, "Exit code 1 from ffprobe, output: #{inspect(error)}"}

      error ->
        IO.inspect(error, label: [IO.ANSI.yellow(), "ai_ocr ffprobe"])
        {:error, "Failed to extract text from image: #{inspect(error)}"}
    end

    result = TesseractOcr.read(new_file_path)
    IO.inspect(result, label: [IO.ANSI.yellow(), "ocr text"])
  end
end
