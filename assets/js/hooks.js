import * as pdfjsLib from 'pdfjs-dist';
import { PDFWorker } from 'pdfjs-dist';

// Set the PDF.js worker source
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';

// PDF Viewer Hook
const PdfViewer = {
  mounted() {
    this.pdfDoc = null;
    this.pageNum = 1;
    this.pageRendering = false;
    this.pageNumPending = null;
    this.scale = 1.5;
    this.canvas = document.getElementById('pdf-canvas');
    this.ctx = this.canvas.getContext('2d');
    this.documentId = this.el.dataset.documentId;
    this.filePath = this.el.dataset.filePath;

    // Load the PDF
    this.loadPdf();

    // Listen for page change events
    this.handleEvent('change-page', ({ page }) => {
      this.queueRenderPage(page);
    });
  },

  loadPdf() {
    // Get the file path from the data attribute
    const url = `/${this.filePath}`;

    // Load the PDF
    pdfjsLib.getDocument(url).promise.then(pdfDoc => {
      this.pdfDoc = pdfDoc;
      this.renderPage(this.pageNum);
    }).catch(error => {
      console.error('Error loading PDF:', error);
    });
  },

  renderPage(num) {
    this.pageRendering = true;

    // Get the page
    this.pdfDoc.getPage(num).then(page => {
      // Set the scale
      const viewport = page.getViewport({ scale: this.scale });
      this.canvas.height = viewport.height;
      this.canvas.width = viewport.width;

      // Render the page
      const renderContext = {
        canvasContext: this.ctx,
        viewport: viewport
      };

      const renderTask = page.render(renderContext);

      // Wait for rendering to finish
      renderTask.promise.then(() => {
        this.pageRendering = false;

        if (this.pageNumPending !== null) {
          // New page rendering is pending
          this.renderPage(this.pageNumPending);
          this.pageNumPending = null;
        }
      });
    });
  },

  queueRenderPage(num) {
    if (this.pageRendering) {
      this.pageNumPending = num;
    } else {
      this.renderPage(num);
    }
  }
};

// Image Viewer Hook
const ImageViewer = {
  mounted() {
    this.documentId = this.el.dataset.documentId;
    this.filePath = this.el.dataset.filePath;

    // Nothing special needed for images as they are displayed with an <img> tag
  }
};

// Selection Handler Hook - Completely rewritten with a direct DOM approach
const SelectionHandler = {
  mounted() {
    console.log('SelectionHandler mounted - fixed version');

    // Initialize state
    this.isSelecting = false;
    this.startX = 0;
    this.startY = 0;
    this.endX = 0;
    this.endY = 0;
    this.boxCount = 0;
    this.boxes = [];

    // Create a style element for our CSS
    const style = document.createElement('style');
    style.textContent = `
      #selection-layer {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        pointer-events: auto !important;
      }
      .selection-box {
        position: absolute !important;
        border: 2px solid #3b82f6 !important;
        background-color: rgba(59, 130, 246, 0.1) !important;
        pointer-events: none !important;
        z-index: 1000 !important;
      }
      .selection-box-label {
        position: absolute !important;
        top: -20px !important;
        left: 0 !important;
        background-color: #3b82f6 !important;
        color: white !important;
        padding: 2px 6px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        z-index: 1001 !important;
      }
      .temp-selection-box {
        position: absolute !important;
        border: 2px dashed #3b82f6 !important;
        background-color: rgba(59, 130, 246, 0.1) !important;
        pointer-events: none !important;
        z-index: 999 !important;
      }
      #image-viewer {
        overflow: auto !important;
        height: 100% !important;
      }
      #image-viewer img {
        max-width: 100% !important;
        display: block !important;
      }
    `;
    document.head.appendChild(style);

    // Create temporary selection box for drawing
    this.tempBox = document.createElement('div');
    this.tempBox.className = 'temp-selection-box';
    this.tempBox.style.display = 'none';
    this.el.appendChild(this.tempBox);

    // Add event listeners
    this.el.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.el.addEventListener('mousemove', this.handleMouseMove.bind(this));
    this.el.addEventListener('mouseup', this.handleMouseUp.bind(this));

    // Listen for clear selections event
    this.handleEvent('clear-selections', () => {
      console.log('Clear selections event received');
      this.clearAllBoxes();
    });
  },

  handleMouseDown(e) {
    // Start selection
    this.isSelecting = true;

    // Get starting coordinates
    const rect = this.el.getBoundingClientRect();
    this.startX = e.clientX - rect.left;
    this.startY = e.clientY - rect.top;

    // Show and position temporary box
    this.tempBox.style.display = 'block';
    this.tempBox.style.left = `${this.startX}px`;
    this.tempBox.style.top = `${this.startY}px`;
    this.tempBox.style.width = '0';
    this.tempBox.style.height = '0';
  },

  handleMouseMove(e) {
    if (!this.isSelecting) return;

    // Get current coordinates
    const rect = this.el.getBoundingClientRect();
    this.endX = e.clientX - rect.left;
    this.endY = e.clientY - rect.top;

    // Calculate box dimensions
    const left = Math.min(this.startX, this.endX);
    const top = Math.min(this.startY, this.endY);
    const width = Math.abs(this.endX - this.startX);
    const height = Math.abs(this.endY - this.startY);

    // Update temporary box
    this.tempBox.style.left = `${left}px`;
    this.tempBox.style.top = `${top}px`;
    this.tempBox.style.width = `${width}px`;
    this.tempBox.style.height = `${height}px`;
  },

  handleMouseUp(e) {
    if (!this.isSelecting) return;

    // End selection
    this.isSelecting = false;

    // Get final coordinates
    const rect = this.el.getBoundingClientRect();
    this.endX = e.clientX - rect.left;
    this.endY = e.clientY - rect.top;

    // Calculate box dimensions
    const left = Math.min(this.startX, this.endX);
    const top = Math.min(this.startY, this.endY);
    const width = Math.abs(this.endX - this.startX);
    const height = Math.abs(this.endY - this.startY);

    // Hide temporary box
    this.tempBox.style.display = 'none';

    // Only create a box if it's big enough
    if (width > 5 && height > 5) {
      // Create permanent box
      this.createBox(left, top, width, height);

      // Clear any text selection
      if (window.getSelection) {
        window.getSelection().removeAllRanges();
      }
    }
  },

  createBox(left, top, width, height) {
    console.log('Creating box:', left, top, width, height);

    // Create a new box
    const boxId = this.boxCount++;
    const box = document.createElement('div');
    box.id = `selection-box-${boxId}`;
    box.dataset.boxId = boxId; // Store the boxId as a data attribute for easier access
    box.className = 'selection-box';
    box.style.left = `${left}px`;
    box.style.top = `${top}px`;
    box.style.width = `${width}px`;
    box.style.height = `${height}px`;

    // Create a container for the label and delete button
    const labelContainer = document.createElement('div');
    labelContainer.className = 'selection-box-label-container';
    labelContainer.style.position = 'absolute';
    labelContainer.style.top = '-24px';
    labelContainer.style.left = '0';
    labelContainer.style.display = 'flex';
    labelContainer.style.alignItems = 'center';
    labelContainer.style.pointerEvents = 'auto'; // Ensure the container can receive click events
    labelContainer.style.zIndex = '1003';

    // Add label
    const label = document.createElement('div');
    label.className = 'selection-box-label';
    label.textContent = `#${boxId + 1}`;
    label.style.backgroundColor = '#3b82f6';
    label.style.color = 'white';
    label.style.padding = '2px 6px';
    label.style.borderRadius = '3px';
    label.style.fontSize = '12px';
    label.style.zIndex = '1003';
    labelContainer.appendChild(label);

    // Add delete button
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'selection-box-delete';
    deleteBtn.innerHTML = '&times;';
    deleteBtn.style.backgroundColor = '#ef4444';
    deleteBtn.style.color = 'white';
    deleteBtn.style.border = 'none';
    deleteBtn.style.borderRadius = '3px';
    deleteBtn.style.marginLeft = '4px';
    deleteBtn.style.width = '20px';
    deleteBtn.style.height = '20px';
    deleteBtn.style.fontSize = '14px';
    deleteBtn.style.lineHeight = '1';
    deleteBtn.style.cursor = 'pointer';
    deleteBtn.style.display = 'flex';
    deleteBtn.style.alignItems = 'center';
    deleteBtn.style.justifyContent = 'center';
    deleteBtn.style.zIndex = '1003';
    deleteBtn.style.pointerEvents = 'auto'; // Ensure the button can receive click events

    // Add event listener to delete button
    const self = this; // Store reference to 'this' for use in event handler
    deleteBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      e.preventDefault();
      console.log('Delete button clicked for box:', boxId);
      self.deleteBox(boxId);
    });

    labelContainer.appendChild(deleteBtn);
    box.appendChild(labelContainer);

    // Add to document
    this.el.appendChild(box);
    console.log('Box added to DOM:', box.id);

    // Store box reference
    this.boxes.push(box);
    console.log('Current boxes:', this.boxes.length);

    // Get the image element for coordinate transformation
    const imageElement = document.querySelector('#image-viewer img');
    let scaleX = 1;
    let scaleY = 1;

    if (imageElement) {
      // Calculate scaling factors
      const naturalWidth = imageElement.naturalWidth;
      const naturalHeight = imageElement.naturalHeight;
      const displayedWidth = imageElement.offsetWidth;
      const displayedHeight = imageElement.offsetHeight;

      scaleX = naturalWidth / displayedWidth;
      scaleY = naturalHeight / displayedHeight;

      console.log('Image dimensions for scaling:', {
        naturalWidth,
        naturalHeight,
        displayedWidth,
        displayedHeight,
        scaleX,
        scaleY
      });
    }

    // Transform display coordinates to actual image coordinates
    const actualX1 = Math.round(left * scaleX);
    const actualY1 = Math.round(top * scaleY);
    const actualX2 = Math.round((left + width) * scaleX);
    const actualY2 = Math.round((top + height) * scaleY);

    console.log('Coordinate transformation:', {
      display: { x1: left, y1: top, x2: left + width, y2: top + height },
      actual: { x1: actualX1, y1: actualY1, x2: actualX2, y2: actualY2 }
    });

    // Create selection data with both display and actual coordinates
    const selectionData = {
      id: boxId,
      // Actual image coordinates for backend processing
      x1: actualX1,
      y1: actualY1,
      x2: actualX2,
      y2: actualY2,
      // Display coordinates for UI
      displayX1: left,
      displayY1: top,
      displayX2: left + width,
      displayY2: top + height,
      text: ''
    };

    // Send all selections to server
    const selections = this.boxes.map((box, index) => {
      // Get display coordinates
      const displayX1 = parseFloat(box.style.left);
      const displayY1 = parseFloat(box.style.top);
      const displayWidth = parseFloat(box.style.width);
      const displayHeight = parseFloat(box.style.height);
      const displayX2 = displayX1 + displayWidth;
      const displayY2 = displayY1 + displayHeight;

      // Get the image element for coordinate transformation
      const imageElement = document.querySelector('#image-viewer img');
      let scaleX = 1;
      let scaleY = 1;

      if (imageElement) {
        // Calculate scaling factors
        const naturalWidth = imageElement.naturalWidth;
        const naturalHeight = imageElement.naturalHeight;
        const displayedWidth = imageElement.offsetWidth;
        const displayedHeight = imageElement.offsetHeight;

        scaleX = naturalWidth / displayedWidth;
        scaleY = naturalHeight / displayedHeight;
      }

      // Transform to actual image coordinates
      const actualX1 = Math.round(displayX1 * scaleX);
      const actualY1 = Math.round(displayY1 * scaleY);
      const actualX2 = Math.round(displayX2 * scaleX);
      const actualY2 = Math.round(displayY2 * scaleY);

      return {
        id: index,
        // Actual image coordinates for backend processing
        x1: actualX1,
        y1: actualY1,
        x2: actualX2,
        y2: actualY2,
        // Display coordinates for UI
        displayX1: displayX1,
        displayY1: displayY1,
        displayX2: displayX2,
        displayY2: displayY2,
        text: ''
      };
    });

    // Only send the selections_updated event, not the individual selection_made event
    this.pushEvent('selections_updated', { selections });

    return boxId;
  },

  deleteBox(boxId) {
    console.log(`Attempting to delete box with ID: ${boxId}`);
    console.log(`Current boxes:`, this.boxes.map(box => box.id));

    // Find the box with the given ID
    const boxIndex = this.boxes.findIndex(box => box.id === `selection-box-${boxId}`);
    console.log(`Box index found: ${boxIndex}`);

    if (boxIndex !== -1) {
      const box = this.boxes[boxIndex];
      console.log(`Found box to delete:`, box);

      // Remove the box from the DOM
      if (box.parentNode) {
        console.log(`Removing box from DOM`);
        box.parentNode.removeChild(box);
      }

      // Remove the box from our array
      this.boxes.splice(boxIndex, 1);
      console.log(`Removed box from array. Remaining boxes:`, this.boxes.length);

      // Update selections and send to server
      if (this.boxes.length > 0) {
        const selections = this.boxes.map((box, index) => {
          // Get display coordinates
          const displayX1 = parseFloat(box.style.left);
          const displayY1 = parseFloat(box.style.top);
          const displayWidth = parseFloat(box.style.width);
          const displayHeight = parseFloat(box.style.height);
          const displayX2 = displayX1 + displayWidth;
          const displayY2 = displayY1 + displayHeight;

          // Get the image element for coordinate transformation
          const imageElement = document.querySelector('#image-viewer img');
          let scaleX = 1;
          let scaleY = 1;

          if (imageElement) {
            // Calculate scaling factors
            const naturalWidth = imageElement.naturalWidth;
            const naturalHeight = imageElement.naturalHeight;
            const displayedWidth = imageElement.offsetWidth;
            const displayedHeight = imageElement.offsetHeight;

            scaleX = naturalWidth / displayedWidth;
            scaleY = naturalHeight / displayedHeight;
          }

          // Transform to actual image coordinates
          const actualX1 = Math.round(displayX1 * scaleX);
          const actualY1 = Math.round(displayY1 * scaleY);
          const actualX2 = Math.round(displayX2 * scaleX);
          const actualY2 = Math.round(displayY2 * scaleY);

          const selection = {
            id: index,
            // Actual image coordinates for backend processing
            x1: actualX1,
            y1: actualY1,
            x2: actualX2,
            y2: actualY2,
            // Display coordinates for UI
            displayX1: displayX1,
            displayY1: displayY1,
            displayX2: displayX2,
            displayY2: displayY2,
            text: ''
          };
          console.log(`Mapped selection:`, selection);
          return selection;
        });

        console.log(`Sending updated selections to server:`, selections);
        // Send updated selections to server
        this.pushEvent('selections_updated', { selections });
      } else {
        console.log(`No boxes left, clearing selections`);
        // If no boxes left, clear selections
        this.pushEvent('selections_cleared', {});
      }
    } else {
      console.error(`Box with ID selection-box-${boxId} not found!`);
      console.log(`Available boxes:`, this.boxes.map(box => box.id));
    }
  },

  clearAllBoxes() {
    console.log(`Clearing ${this.boxes.length} boxes`);

    // Remove all boxes
    this.boxes.forEach(box => {
      if (box.parentNode) {
        box.parentNode.removeChild(box);
      }
    });

    // Reset state
    this.boxes = [];
    this.boxCount = 0;

    // Notify server
    this.pushEvent('selections_cleared', {});
  }
};

export default {
  PdfViewer,
  ImageViewer,
  SelectionHandler
};
