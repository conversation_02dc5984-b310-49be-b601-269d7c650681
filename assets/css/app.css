@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* This file is for your main application CSS */

#selection-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 1000 !important;
}

[id^="selection-box-"] {
  position: absolute !important;
  border: 2px solid #3b82f6 !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
  z-index: 1002 !important;
}

.selection-box-label-container {
  position: absolute !important;
  top: -24px !important;
  left: 0 !important;
  display: flex !important;
  align-items: center !important;
  z-index: 1003 !important;
  pointer-events: auto !important;
}

.selection-box-label {
  background-color: #3b82f6 !important;
  color: white !important;
  padding: 2px 6px !important;
  border-radius: 3px !important;
  font-size: 12px !important;
}

.selection-box-delete {
  background-color: #ef4444 !important;
  color: white !important;
  border: none !important;
  border-radius: 3px !important;
  margin-left: 4px !important;
  width: 20px !important;
  height: 20px !important;
  font-size: 14px !important;
  line-height: 1 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: transform 0.1s ease-in-out !important;
  pointer-events: auto !important;
}

.selection-box-delete:hover {
  transform: scale(1.1) !important;
}

#image-viewer {
  overflow: auto !important;
  height: 80vh !important;
}

#image-viewer img {
  max-width: 100% !important;
}